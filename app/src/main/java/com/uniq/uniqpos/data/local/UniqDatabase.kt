package com.uniq.uniqpos.data.local

import androidx.room.AutoMigration
import androidx.room.Database
import androidx.room.DeleteTable
import androidx.room.RoomDatabase
import androidx.room.migration.AutoMigrationSpec
import com.uniq.uniqpos.data.local.dao.*
import com.uniq.uniqpos.data.local.entity.*
import retrofit2.http.HEAD

/**
 * Created by ANNASBlackHat on 10/10/2017.
 */

@Database(entities = [(SubCategoryEntity::class), (ProductEntity::class), (PrinterEntity::class), (SalesEntity::class),
    CashRecapEntity::class, (GratuityEntity::class), (RefundEntity::class), (TaxEntity::class), (LastSyncEntity::class),
    TmpSalesEntity::class, (BankEntity::class), (DeleteRecordEntity::class), (ProductDescEntity::class), (ShiftEntity::class),
    OpenShiftEntity::class, (DiningTableEntity::class), (PrinterClosingShiftEntity::class), (PendingPrintEntity::class),
    PrinterTicketEntity::class, (MultiplePriceEntity::class), (EmployeeEntity::class), (LinkMenuEntity::class),
    MemberEntity::class, ReservationEntity::class, ProductVariantEntity::class,
    MemberTypeEntity::class, ProductTypeEntity::class, UnitEntity::class, PurchaseReportCategoryEntity::class,
    CategoryEntity::class, PiutangEntity::class, PiutangHistoryEntity::class, SupplierEntity::class, OperationalCostEntity::class,
    PromotionEntity::class, OrderSalesEntity::class, SalesTagEntity::class, KitchenDisplayEntity::class, TransactionSettingEntity::class],
        version = 31, exportSchema = true,)

abstract class UniqDatabase : RoomDatabase() {

    abstract fun productDao(): ProductDao

    abstract fun settingDao(): SettingDao

    abstract fun salesDao(): SalesDao

    abstract fun outletDao(): OutletDao

    abstract fun lastSyncDao(): LastSyncDao

    abstract fun deleteRecordDao(): DeleteRecordDao

    abstract fun customerDao(): CustomerDao

    abstract fun purchaseDao(): PurchaseDao

    abstract fun promotionDao(): PromotionDao

    @DeleteTable(tableName = "order_sales")
    class Migration24: AutoMigrationSpec
}
