package com.uniq.uniqpos.data.local.entity

//import kotlinx.android.parcel.Parcelize
import android.os.Parcelable
import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.data.local.converter.MemberDetailConverter
import com.uniq.uniqpos.data.local.converter.OrdersConverter
import com.uniq.uniqpos.data.local.converter.PaymentConverter
import com.uniq.uniqpos.data.local.converter.PromotionConverter
import com.uniq.uniqpos.data.local.converter.SalesRefundEntityConverter
import com.uniq.uniqpos.data.local.converter.SalesTagConverter
import com.uniq.uniqpos.data.local.converter.TaxSaleConverter
import com.uniq.uniqpos.data.remote.model.Member
import com.uniq.uniqpos.data.remote.model.Payment
import com.uniq.uniqpos.data.remote.model.Promotion
import com.uniq.uniqpos.model.Discount
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.model.TaxSales
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.safeToInt
import kotlinx.android.parcel.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

/**
 * Created by ANNASBlackHat on 21/10/2017.
 */

@Entity(tableName = "sales")
@TypeConverters(
    OrdersConverter::class, TaxSaleConverter::class, PaymentConverter::class,
    PromotionConverter::class, MemberDetailConverter::class, SalesTagConverter::class,
    SalesRefundEntityConverter::class
)
@Parcelize
data class SalesEntity(
    @PrimaryKey
    var noNota: String,
    var orderList: ArrayList<Order>? = null,
    var grandTotal: Int = 0,
    var customer: String? = "",
    var customersQty: Int = 1,
    var timePrediction: Int = 0,
    var taxes: ArrayList<TaxSales>? = null,
    var payment: String = "",
    var payments: ArrayList<Payment> = ArrayList(),
    var status: String? = "Success",
    var employeeID: Int = 0,
    var employeeName: String? = "",
    var outletID: Int = 0,
    var outletName: String? = "",
    var openShiftId: Long = 0,
    @Embedded
    var discount: Discount? = null,
    var timeCreated: Long = System.currentTimeMillis(),
    var timeModified: Long = System.currentTimeMillis(),
    var table: String = "",
    var displayNota: String = "",
    var synced: Boolean = false,
    var receiptReceiver: String? = null,
    var memberId: String? = null,
    var memberDetail: Member? = null,
    var promotions: ArrayList<Promotion>? = null,
    var refundReason: String? = null,
    @SerializedName("sales_tag")
    var salesTag: SalesTagEntity? = null,
    var salesRefund: RefundEntity? = null,
    var note: String? = null
) : Parcelable {
    @IgnoredOnParcel
    @Ignore
    var items: String? = null
        get() {
            val value = StringBuilder()
            orderList?.forEach { value.append("${it.product?.name}, ") }
            return value.toString().substring(0, if (value.length > 2) value.length - 2 else 0)
        }
}

fun SalesEntity.extractAllTaxes(): java.util.ArrayList<TaxSales>{
    val taxList = java.util.ArrayList<TaxSales>()
    taxes?.let { taxList.addAll(it) }
    discount?.takeIf { it.discount > 0 }?.let {
        taxList.add(
            TaxSales(
                null,
                "Discount",
                it.discountNominal,
                it.discountType,
                it.discount,
                Constant.TAX_CATEGORY_DISC
            )
        )
    }
    discount?.takeIf { it.voucher > 0 }?.let {
        taxList.add(
            TaxSales(
                null,
                "Voucher",
                it.voucherNominal,
                it.voucherType,
                it.voucher,
                Constant.TAX_CATEGORY_VOUCHER
            )
        )
    }
    promotions?.forEach { promotion ->
        taxList.add(
            TaxSales(
                null,
                promotion.name,
                promotion.value.safeToInt(),
                "deals",
                promotion.value.safeToInt(),
                Constant.TAX_CATEGORY_VOUCHER
            )
        )
    }
    return taxList
}