package com.uniq.uniqpos.di


import androidx.room.Room
import android.content.Context
import android.content.res.AssetManager
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.data.local.UniqDatabase
import com.uniq.uniqpos.data.local.dao.SalesDao
import com.uniq.uniqpos.data.local.migration.*
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.remote.auth.AuthenticationInterceptor
import com.uniq.uniqpos.util.appendLog
import com.uniq.uniqpos.util.kds.KdsConnection
import com.uniq.uniqpos.util.printer.PrinterManager
import com.uniq.uniqpos.util.printer.PrinterSocket
import dagger.Module
import dagger.Provides
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

/**
 * Created by ANNASBlackHat on 10/10/2017.
 */

@Module(includes = [(ServiceModule::class), (ViewModelModule::class), (DaoModule::class)])
class AppModule {

    @Singleton
    @Provides
    fun provideLoggingInterceptor(app: Context): HttpLoggingInterceptor {
//        val interceptor = HttpLoggingInterceptor { log ->
//            appendLog(log, "server", app)
//            Timber.d(log)
//        }
        val interceptor = HttpLoggingInterceptor()
        interceptor.level = if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE
        return interceptor
    }

    @Singleton
    @Provides
    fun provideAuthInterceptor(app: Context): AuthenticationInterceptor {
        return AuthenticationInterceptor(app)
    }

    @Singleton
    @Provides
    fun provideOkHttpClient(interceptor: HttpLoggingInterceptor, authInterceptor: AuthenticationInterceptor): OkHttpClient {
        val okHttpClient = OkHttpClient.Builder()
        okHttpClient.connectTimeout(60, TimeUnit.SECONDS)
        okHttpClient.readTimeout(60, TimeUnit.SECONDS)
        okHttpClient.addInterceptor(interceptor)
        okHttpClient.addInterceptor(authInterceptor)

//        val client = UnsafeOkHttpClient.getUnsafeOkHttpClient().newBuilder()
//        client.connectTimeout(60, TimeUnit.SECONDS)
//        client.readTimeout(60, TimeUnit.SECONDS)
//        client.addInterceptor(interceptor)
//        client.addInterceptor(authInterceptor)

        return okHttpClient.build()
    }

    @Singleton
    @Provides
    fun provideGson(): Gson {
        return GsonBuilder()
                .setDateFormat("yyyy-MM-dd'T'HH:mm:ss")
                .create()
    }

    @Singleton
    @Provides
    fun provideRetrofit(okHttpClient: OkHttpClient, gson: Gson): Retrofit {
        return Retrofit.Builder()
                .baseUrl(BuildConfig.BASE_URL)
                .addConverterFactory(GsonConverterFactory.create(gson))
//                .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
                .client(okHttpClient)
                .build()
    }

    @Singleton
    @Provides
    fun provideDatabase(app: Context): UniqDatabase {
        return Room.databaseBuilder(app, UniqDatabase::class.java, "${BuildConfig.APPLICATION_ID}.db")
                .addMigrations(MIGRATION_19_20(), MIGRATION_20_21(), MIGRATION_21_22(), MIGRATION_22_23(),
                    MIGRATION_23_24(), MIGRATION_24_25(), MIGRATION_25_26(), MIGRATION_26_27(),
                    MIGRATION_27_28(), MIGRATION_28_29(), MIGRATION_29_30(), MIGRATION_30_31()
                )
//                .fallbackToDestructiveMigration()
                .build()
    }

    @Singleton
    @Provides
    fun provideSharedPreference(app: Context): SharedPref {
        return SharedPref(app)
    }

    @Singleton
    @Provides
    fun provideBoolean(): Boolean = true

    @Singleton
    @Provides
    fun providePrinterManager(context: Context, salesDao: SalesDao): PrinterManager {
        return PrinterManager(context, salesDao)
    }

    @Singleton
    @Provides
    fun providePrinterSocket(printerManager: PrinterManager, context: Context): PrinterSocket {
        return PrinterSocket(printerManager, context)
    }

    @Singleton
    @Provides
    fun provideFirebaseRemoteConfig(): FirebaseRemoteConfig{
        return Firebase.remoteConfig
    }

    @Singleton
    @Provides
    fun provideAssetManager(context: Context): AssetManager{
        return context.assets
    }

    @Singleton
    @Provides
    fun provideKdsConnection(assetManager: AssetManager): KdsConnection{
        return KdsConnection(assetManager)
    }
}