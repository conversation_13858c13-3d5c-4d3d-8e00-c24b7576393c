package com.uniq.uniqpos.ui.form

import android.content.Context
import android.graphics.Color
import android.view.View
import android.widget.Button
import android.widget.HorizontalScrollView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.uniq.uniqpos.R

class SingleChoiceComponent(config: FormFieldConfig) : FormFieldComponent(config) {
    private var selectedValue: String? = null
    private val chipButtons = mutableListOf<Button>()

    override fun createView(context: Context): View {
        return LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(0, 0, 0, dpToPx(context, 16))

            // Label
            addView(TextView(context).apply {
                text = if (config.isRequired) "${config.label} *" else config.label
                setTextColor(Color.parseColor("#CCCCCC"))
                textSize = 14f
                setPadding(0, 0, 0, dpToPx(context, 8))
            })

            // Horizontal ScrollView for chips
            addView(HorizontalScrollView(context).apply {
                isHorizontalScrollBarEnabled = false

                // Container for chips
                addView(LinearLayout(context).apply {
                    orientation = LinearLayout.HORIZONTAL
                    setPadding(0, 0, dpToPx(context, 16), 0)

                    config.optionValues?.forEach { option ->
                        val chipButton = Button(context).apply {
                            text = option
                            textSize = 12f
                            isAllCaps = false
                            setTextColor(Color.WHITE)
                            background = ContextCompat.getDrawable(context, R.drawable.btn_round)
                            setPadding(dpToPx(context, 16), 0, dpToPx(context, 16), 0)
                            minHeight = dpToPx(context, 40)
                            tag = option

                            // Set margins
                            val params = LinearLayout.LayoutParams(
                                LinearLayout.LayoutParams.WRAP_CONTENT,
                                LinearLayout.LayoutParams.WRAP_CONTENT
                            )
                            params.setMargins(0, 0, dpToPx(context, 8), 0)
                            layoutParams = params

                            setOnClickListener {
                                selectChip(option)
                            }
                        }

                        chipButtons.add(chipButton)
                        addView(chipButton)
                    }
                })
            })
        }
    }

    private fun selectChip(value: String) {
        selectedValue = value

        // Update chip appearances
        chipButtons.forEach { button ->
            if (button.tag == value) {
                // Selected state - green background
                button.background = ContextCompat.getDrawable(button.context, R.drawable.btn_round_green)
                button.setTextColor(Color.WHITE)
            } else {
                // Unselected state - gray background
                button.background = ContextCompat.getDrawable(button.context, R.drawable.btn_round)
                button.setTextColor(Color.WHITE)
            }
        }
    }

    override fun getValue(): String? = selectedValue

    override fun validate(): ValidationResult {
        val value = getValue()
        if (config.isRequired && value.isNullOrBlank()) {
            return ValidationResult.Error("Please select ${config.label}")
        }
        return ValidationResult.Success
    }

    override fun reset() {
        selectedValue = null
        // Reset all chips to unselected state
        chipButtons.forEach { button ->
            button.background = ContextCompat.getDrawable(button.context, R.drawable.btn_round)
            button.setTextColor(Color.WHITE)
        }
    }

    private fun dpToPx(context: Context, dp: Int): Int {
        return (dp * context.resources.displayMetrics.density).toInt()
    }
}
