package com.uniq.uniqpos.ui.form

import android.content.Context
import android.graphics.Color
import android.view.View
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.TextView
import com.uniq.uniqpos.R

class SingleChoiceComponent(config: FormFieldConfig) : FormFieldComponent(config) {
    private var radioGroup: RadioGroup? = null
    
    override fun createView(context: Context): View {
        return LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(0, 0, 0, dpToPx(context, 16))
            
            // Label
            addView(TextView(context).apply {
                text = if (config.isRequired) "${config.label} *" else config.label
                setTextColor(Color.parseColor("#CCCCCC"))
                textSize = 14f
                setPadding(0, 0, 0, dpToPx(context, 8))
            })
            
            // Radio Group
            radioGroup = RadioGroup(context).apply {
                orientation = RadioGroup.VERTICAL
                
                config.optionValues?.forEachIndexed { index, option ->
                    addView(RadioButton(context).apply {
                        id = View.generateViewId()
                        text = option
                        setTextColor(Color.WHITE)
                        tag = option // Store the value in tag
                        setPadding(dpToPx(context, 12), dpToPx(context, 8), dpToPx(context, 12), dpToPx(context, 8))
                        setBackgroundResource(R.drawable.bg_radio_option) // Use radio option background
                    })
                }
            }
            addView(radioGroup)
        }
    }
    
    override fun getValue(): String? {
        val selectedId = radioGroup?.checkedRadioButtonId
        if (selectedId != null && selectedId != -1) {
            val selectedRadio = radioGroup?.findViewById<RadioButton>(selectedId)
            return selectedRadio?.tag as? String
        }
        return null
    }
    
    override fun validate(): ValidationResult {
        val value = getValue()
        if (config.isRequired && value.isNullOrBlank()) {
            return ValidationResult.Error("Please select ${config.label}")
        }
        return ValidationResult.Success
    }
    
    override fun reset() {
        radioGroup?.clearCheck()
    }
    
    private fun dpToPx(context: Context, dp: Int): Int {
        return (dp * context.resources.displayMetrics.density).toInt()
    }
}
