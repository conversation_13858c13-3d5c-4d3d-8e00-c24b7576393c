package com.uniq.uniqpos.ui.form

import android.content.Context
import android.graphics.Color
import android.view.View
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import com.uniq.uniqpos.R

class TextFieldComponent(config: FormFieldConfig) : FormFieldComponent(config) {
    private var editText: EditText? = null
    
    override fun createView(context: Context): View {
        return LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(0, 0, 0, dpToPx(context, 16))
            
            // Label
            addView(TextView(context).apply {
                text = if (config.isRequired) "${config.label} *" else config.label
                setTextColor(Color.parseColor("#CCCCCC"))
                textSize = 14f
                setPadding(0, 0, 0, dpToPx(context, 8))
            })
            
            // Input
            editText = EditText(context).apply {
                hint = "Enter ${config.label.lowercase()}..."
                setBackgroundResource(R.drawable.rounded_edittext_background) // Use existing input background
                setTextColor(Color.WHITE)
                setHintTextColor(Color.parseColor("#888888"))
                setPadding(dpToPx(context, 12), dpToPx(context, 12), dpToPx(context, 12), dpToPx(context, 12))
            }
            addView(editText)
        }
    }
    
    override fun getValue(): String? = editText?.text?.toString()?.trim()
    
    override fun validate(): ValidationResult {
        val value = getValue()
        if (config.isRequired && value.isNullOrBlank()) {
            return ValidationResult.Error("${config.label} is required")
        }
        return ValidationResult.Success
    }
    
    override fun reset() {
        editText?.setText("")
    }
    
    private fun dpToPx(context: Context, dp: Int): Int {
        return (dp * context.resources.displayMetrics.density).toInt()
    }
}
