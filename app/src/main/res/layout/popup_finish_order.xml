<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.view.transaction.dialog.SaveOrderDialog.SaveOrder" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/dialog_round"
            android:orientation="vertical"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:paddingBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins"
                android:paddingTop="5dp"
                android:text="Transaction Info"
                android:textColor="@color/grey_light"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="@id/view_close"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/view_close" />

            <View
                android:id="@+id/view_close"
                android:layout_width="23dp"
                android:layout_height="23dp"
                android:layout_marginTop="9dp"
                android:background="@drawable/round_white"
                android:backgroundTint="@color/text_grey"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/img_close"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:padding="4dp"
                app:srcCompat="@drawable/met_ic_close"
                app:layout_constraintBottom_toBottomOf="@id/view_close"
                app:layout_constraintEnd_toEndOf="@id/view_close"
                app:layout_constraintStart_toStartOf="@id/view_close"
                app:layout_constraintTop_toTopOf="@id/view_close" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/layout_customer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="9dp"
                android:textColorHint="@color/text_grey_light"
                android:theme="@style/InputStyle"
                app:layout_constraintTop_toBottomOf="@id/rec_view_tag">

                <!--                <com.google.android.material.textfield.TextInputEditText-->
                <!--                    android:id="@+id/edtCustomer"-->
                <!--                    android:visibility="gone"-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:hint="@string/customer"-->
                <!--                    android:inputType="textCapWords"-->
                <!--                    android:maxLength="25"-->
                <!--                    android:textColor="@color/text_grey_light"-->
                <!--                    app:drawableBound="@{model.imgRight}"-->
                <!--                    app:position="@{`right`}" />-->

                <androidx.appcompat.widget.AppCompatAutoCompleteTextView
                    android:id="@+id/edtCustomer"
                    drawableBoundAutoComp="@{model.imgRight}"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/customer"
                    android:inputType="textCapWords"
                    android:maxLength="50"
                    android:selectAllOnFocus="true"
                    android:textColor="@color/text_grey_light" />

            </com.google.android.material.textfield.TextInputLayout>

            <Button
                android:id="@+id/btn_scan_member"
                android:layout_width="50dp"
                android:layout_height="0dp"
                android:background="@android:color/transparent"
                app:layout_constraintBottom_toBottomOf="@id/layout_customer"
                app:layout_constraintEnd_toEndOf="@id/layout_customer"
                app:layout_constraintTop_toTopOf="@id/layout_customer" />

            <TextView
                android:id="@+id/txt_use_secret_code"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:paddingLeft="9dp"
                android:text="secret code"
                android:textColor="@color/greeen_background"
                android:textSize="13sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_customer" />

            <TextView
                android:id="@+id/txt_dot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingBottom="12dp"
                android:text="."
                android:textColor="@color/text_grey_light"
                android:textSize="27sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/txt_use_secret_code"
                app:layout_constraintEnd_toStartOf="@id/txt_use_secret_code"
                app:layout_constraintStart_toEndOf="@id/txt_search_member"
                app:layout_constraintTop_toTopOf="@id/txt_use_secret_code" />

            <TextView
                android:id="@+id/txt_search_member"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="9dp"
                android:paddingLeft="9dp"
                android:paddingRight="9dp"
                android:text="search member"
                android:textColor="@color/greeen_background"
                android:textSize="13sp"
                app:layout_constraintEnd_toStartOf="@id/txt_use_secret_code"
                app:layout_constraintTop_toTopOf="@id/txt_use_secret_code" />

            <TextView
                android:id="@+id/txt_member_level"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="3dp"
                android:textColor="@color/text_grey_light"
                android:textSize="13sp"
                app:layout_constraintStart_toStartOf="@id/layout_customer"
                app:layout_constraintTop_toBottomOf="@id/layout_customer"
                tools:text="Level Broze" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/layout_qty_customer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColorHint="@color/text_grey_light"
                android:theme="@style/InputStyle"
                app:layout_constraintTop_toBottomOf="@id/txt_member_level">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtQtyCustomer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/qty_customer"
                    android:inputType="number"
                    android:maxLength="5"
                    android:textColor="@color/text_grey_light" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/layout_prediction"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="9dp"
                android:textColorHint="@color/text_grey_light"
                android:theme="@style/InputStyle"
                app:layout_constraintTop_toBottomOf="@id/layout_qty_customer">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtPrediction"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/queue_time"
                    android:inputType="number"
                    android:maxLength="5"
                    android:textColor="@color/text_grey_light" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/layout_pin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="9dp"
                android:textColorHint="@color/text_grey_light"
                android:theme="@style/InputStyle"
                app:layout_constraintTop_toBottomOf="@id/layout_prediction">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_pin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:hint="PIN"
                    android:inputType="number"
                    android:textColor="@color/text_grey_light" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/layout_table"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="9dp"
                android:textColorHint="@color/text_grey_light"
                android:theme="@style/InputStyle"
                app:layout_constraintTop_toBottomOf="@id/layout_pin">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtTable"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:hint="@string/table"
                    android:textColor="@color/text_grey_light" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/layout_note"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="9dp"
                android:textColorHint="@color/text_grey_light"
                android:theme="@style/InputStyle"
                app:layout_constraintTop_toBottomOf="@id/layout_table">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtNote"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Note (for cart only)"
                    android:inputType="textMultiLine"
                    android:maxLength="100"
                    android:textColor="@color/text_grey_light" />
            </com.google.android.material.textfield.TextInputLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rec_view_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="19dp"
                android:orientation="horizontal"
                tools:itemCount="2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/view_close"
                tools:listitem="@layout/list_item_names" />

            <com.uniq.materialanimation.ButtonLoading
                android:id="@+id/btn_save"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:layout_marginRight="3dp"
                app:buttonColor="@color/greeen_background"
                app:buttonText="@string/save"
                app:layout_constraintEnd_toStartOf="@id/btnBayar"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_note"
                app:textColor="@color/white" />

            <com.uniq.materialanimation.ButtonLoading
                android:id="@+id/btnBayar"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                app:buttonColor="@color/blue_background"
                app:buttonText="@string/pay"
                app:layout_constraintEnd_toStartOf="@id/btnDone"
                app:layout_constraintStart_toEndOf="@id/btn_save"
                app:layout_constraintTop_toTopOf="@id/btn_save" />

            <com.uniq.materialanimation.ButtonLoading
                android:id="@+id/btnDone"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="3dp"
                android:textColor="@android:color/white"
                app:buttonColor="@color/red_background"
                app:buttonText="CLEAR BILL"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/btnBayar"
                app:layout_constraintTop_toTopOf="@id/btn_save" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</layout>
